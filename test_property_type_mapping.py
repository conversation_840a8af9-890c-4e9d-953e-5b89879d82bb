#!/usr/bin/env python3
"""
Test script for property type mapping functionality
"""

import sys
import os
import asyncio
import json
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

from src.utils.property_type_mapper import PropertyTypeMapper, property_type_mapper

def test_property_type_mapping():
    """Test property type mapping functionality"""
    
    print("🔧 Testing Property Type Mapping")
    print("=" * 50)
    
    # Test 1: Basic normalization
    print("\n1. Testing Basic Normalization:")
    test_cases = [
        "SINGLE_FAMILY",
        "MULTI_FAMILY", 
        "DUPLEX",
        "SFR",
        "MFR",
        "single family",
        "multi family",
        "condo",
        "townhouse"
    ]
    
    for test_case in test_cases:
        normalized = property_type_mapper.normalize_to_api_format(test_case)
        print(f"  {test_case:15} -> {normalized}")
    
    # Test 2: List normalization
    print("\n2. Testing List Normalization:")
    test_list = ["SINGLE_FAMILY", "MULTI_FAMILY", "DUPLEX"]
    normalized_list = property_type_mapper.normalize_to_api_format(test_list)
    print(f"  {test_list} -> {normalized_list}")
    
    # Test 3: Section 8 suitability
    print("\n3. Testing Section 8 Suitability:")
    for test_case in ["SFR", "MFR", "CONDO", "MOBILE", "LAND"]:
        suitable = property_type_mapper.is_section8_suitable(test_case)
        print(f"  {test_case:6} -> Section 8 Suitable: {suitable}")
    
    # Test 4: Search parameters
    print("\n4. Testing Search Parameters:")
    params = property_type_mapper.build_search_parameters(["SFR", "MFR"])
    print(f"  Search params: {params}")
    
    # Test 5: Property type descriptions
    print("\n5. Testing Property Type Descriptions:")
    for api_type in property_type_mapper.get_all_api_types():
        description = property_type_mapper.get_property_type_description(api_type)
        print(f"  {api_type:6} -> {description}")
    
    print("\n✅ Property type mapping tests completed!")

def test_config_integration():
    """Test integration with config.json"""
    
    print("\n🔧 Testing Config Integration")
    print("=" * 50)
    
    try:
        # Load config.json
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Check property types in config
        property_specs = config.get("investment_criteria", {}).get("property_specifications", {})
        property_types = property_specs.get("property_types", [])
        
        print(f"\nProperty types in config: {property_types}")
        
        # Test normalization of config property types
        for prop_type in property_types:
            normalized = property_type_mapper.normalize_to_api_format(prop_type)
            suitable = property_type_mapper.is_section8_suitable(normalized)
            print(f"  {prop_type:6} -> {normalized:6} (Section 8: {suitable})")
        
        # Test search parameter generation
        search_params = property_type_mapper.build_search_parameters(property_types)
        print(f"\nGenerated search parameters: {search_params}")
        
        print("\n✅ Config integration tests completed!")
        
    except Exception as e:
        print(f"\n❌ Config integration test failed: {e}")

def test_api_compatibility():
    """Test Real Estate API compatibility"""
    
    print("\n🔧 Testing API Compatibility")
    print("=" * 50)
    
    # Test cases that should work with Real Estate API
    valid_api_types = ["SFR", "MFR", "CONDO", "MOBILE", "LAND", "OTHER"]
    
    print("\nValid API property types:")
    for api_type in valid_api_types:
        params = property_type_mapper.build_search_parameters([api_type])
        print(f"  {api_type:6} -> {params}")
    
    # Test invalid cases
    print("\nInvalid property types (should default to SFR):")
    invalid_types = ["INVALID", "", None, "UNKNOWN"]
    
    for invalid_type in invalid_types:
        try:
            normalized = property_type_mapper.normalize_to_api_format(invalid_type or "")
            print(f"  {str(invalid_type):8} -> {normalized}")
        except Exception as e:
            print(f"  {str(invalid_type):8} -> Error: {e}")
    
    print("\n✅ API compatibility tests completed!")

def test_real_world_scenarios():
    """Test real-world usage scenarios"""
    
    print("\n🔧 Testing Real-World Scenarios")
    print("=" * 50)
    
    # Scenario 1: Section 8 investor looking for SFR and MFR
    print("\n1. Section 8 Investor Scenario:")
    investor_types = ["SINGLE_FAMILY", "MULTI_FAMILY"]
    search_params = property_type_mapper.build_search_parameters(investor_types, section8_only=True)
    print(f"   Input: {investor_types}")
    print(f"   Search params: {search_params}")
    
    # Scenario 2: General investor (all types)
    print("\n2. General Investor Scenario:")
    all_types = list(property_type_mapper.get_all_api_types().keys())
    search_params = property_type_mapper.build_search_parameters(all_types, section8_only=False)
    print(f"   Input: {all_types}")
    print(f"   Search params: {search_params}")
    
    # Scenario 3: Legacy system migration
    print("\n3. Legacy System Migration:")
    legacy_types = ["SINGLE_FAMILY", "MULTI_FAMILY", "DUPLEX", "TOWNHOUSE"]
    normalized = property_type_mapper.normalize_to_api_format(legacy_types)
    print(f"   Legacy types: {legacy_types}")
    print(f"   Normalized: {normalized}")
    
    print("\n✅ Real-world scenario tests completed!")

def main():
    """Run all tests"""
    
    print("🚀 Starting Property Type Mapping Tests")
    print("=" * 60)
    
    try:
        test_property_type_mapping()
        test_config_integration()
        test_api_compatibility()
        test_real_world_scenarios()
        
        print("\n" + "=" * 60)
        print("🎉 All tests completed successfully!")
        print("✅ Property type mapping is working correctly")
        print("✅ Config integration is functional")
        print("✅ API compatibility is verified")
        
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
