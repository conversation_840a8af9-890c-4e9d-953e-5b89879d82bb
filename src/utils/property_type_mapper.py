#!/usr/bin/env python3
"""
Property Type Mapping Utility for Section 8 Monitor
Handles conversion between internal property type representations and Real Estate API codes
"""

from typing import Dict, List, Optional, Union
import logging

class PropertyTypeMapper:
    """
    Utility class for mapping property types between different representations
    """
    
    # Real Estate API property type codes
    API_PROPERTY_TYPES = {
        "SFR": "Single Family Residential",
        "MFR": "Multi Family Residential", 
        "CONDO": "Condominium",
        "MOBILE": "Mobile Home",
        "LAND": "Vacant Land",
        "OTHER": "Other Property Types"
    }
    
    # Legacy to API mapping
    LEGACY_TO_API_MAPPING = {
        "SINGLE_FAMILY": "SFR",
        "MULTI_FAMILY": "MFR",
        "DUPLEX": "MFR",  # Duplex is considered multi-family
        "TOWNHOUSE": "SFR",  # Townhouse is typically single-family
        "CONDO": "CONDO",
        "CONDOMINIUM": "CONDO",
        "MOBILE_HOME": "<PERSON>O<PERSON><PERSON>",
        "MANUFACTURED": "MOBILE",
        "VACANT_LAND": "LAND",
        "LAND": "LAND"
    }
    
    # API to legacy mapping (reverse)
    API_TO_LEGACY_MAPPING = {
        "SFR": "SINGLE_FAMILY",
        "MFR": "MULTI_FAMILY",
        "CONDO": "CONDO",
        "MOBILE": "MOBILE_HOME",
        "LAND": "VACANT_LAND",
        "OTHER": "OTHER"
    }
    
    # Section 8 preferred property types
    SECTION8_PREFERRED = ["SFR", "MFR"]
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def normalize_to_api_format(self, property_type: Union[str, List[str]]) -> Union[str, List[str]]:
        """
        Convert property type(s) to Real Estate API format
        
        Args:
            property_type: Single property type string or list of property types
            
        Returns:
            Normalized property type(s) in API format
        """
        if isinstance(property_type, list):
            return [self._normalize_single_type(pt) for pt in property_type]
        else:
            return self._normalize_single_type(property_type)
    
    def _normalize_single_type(self, property_type: str) -> str:
        """
        Normalize a single property type to API format
        
        Args:
            property_type: Property type string
            
        Returns:
            Normalized property type in API format
        """
        if not property_type:
            return "SFR"  # Default to single family
        
        # Clean and uppercase the input
        clean_type = property_type.strip().upper()
        
        # Check if it's already in API format
        if clean_type in self.API_PROPERTY_TYPES:
            return clean_type
        
        # Check legacy mapping
        if clean_type in self.LEGACY_TO_API_MAPPING:
            return self.LEGACY_TO_API_MAPPING[clean_type]
        
        # Handle common variations
        if "SINGLE" in clean_type or "SFR" in clean_type:
            return "SFR"
        elif "MULTI" in clean_type or "MFR" in clean_type or "DUPLEX" in clean_type:
            return "MFR"
        elif "CONDO" in clean_type:
            return "CONDO"
        elif "MOBILE" in clean_type or "MANUFACTURED" in clean_type:
            return "MOBILE"
        elif "LAND" in clean_type or "VACANT" in clean_type:
            return "LAND"
        else:
            self.logger.warning(f"Unknown property type '{property_type}', defaulting to SFR")
            return "SFR"
    
    def get_section8_types(self) -> List[str]:
        """
        Get property types preferred for Section 8 investments
        
        Returns:
            List of property type codes suitable for Section 8
        """
        return self.SECTION8_PREFERRED.copy()
    
    def is_section8_suitable(self, property_type: str) -> bool:
        """
        Check if a property type is suitable for Section 8 investments
        
        Args:
            property_type: Property type to check
            
        Returns:
            True if suitable for Section 8, False otherwise
        """
        normalized = self.normalize_to_api_format(property_type)
        return normalized in self.SECTION8_PREFERRED
    
    def get_property_type_description(self, property_type: str) -> str:
        """
        Get human-readable description for a property type
        
        Args:
            property_type: Property type code
            
        Returns:
            Human-readable description
        """
        normalized = self.normalize_to_api_format(property_type)
        return self.API_PROPERTY_TYPES.get(normalized, "Unknown Property Type")
    
    def validate_property_types(self, property_types: Union[str, List[str]]) -> bool:
        """
        Validate that property type(s) are valid
        
        Args:
            property_types: Single property type or list of property types
            
        Returns:
            True if all property types are valid, False otherwise
        """
        if isinstance(property_types, str):
            property_types = [property_types]
        
        for pt in property_types:
            normalized = self.normalize_to_api_format(pt)
            if normalized not in self.API_PROPERTY_TYPES:
                return False
        
        return True
    
    def build_search_parameters(self, 
                              property_types: Optional[Union[str, List[str]]] = None,
                              section8_only: bool = True) -> Dict[str, str]:
        """
        Build search parameters for Real Estate API
        
        Args:
            property_types: Specific property types to search for
            section8_only: If True, only include Section 8 suitable types
            
        Returns:
            Dictionary with property type parameters for API search
        """
        if property_types is None:
            if section8_only:
                property_types = self.get_section8_types()
            else:
                property_types = list(self.API_PROPERTY_TYPES.keys())
        
        # Normalize property types
        normalized_types = self.normalize_to_api_format(property_types)
        
        if isinstance(normalized_types, str):
            normalized_types = [normalized_types]
        
        # Filter for Section 8 suitable types if requested
        if section8_only:
            normalized_types = [pt for pt in normalized_types if pt in self.SECTION8_PREFERRED]
        
        # Real Estate API expects individual property_type parameter, not comma-separated
        # For multiple types, we need to make separate API calls or use the first preferred type
        if normalized_types:
            return {"property_type": normalized_types[0]}
        else:
            return {"property_type": "SFR"}  # Default fallback
    
    def get_all_api_types(self) -> Dict[str, str]:
        """
        Get all available API property types with descriptions
        
        Returns:
            Dictionary mapping API codes to descriptions
        """
        return self.API_PROPERTY_TYPES.copy()


# Global instance for easy access
property_type_mapper = PropertyTypeMapper()


def normalize_property_type(property_type: Union[str, List[str]]) -> Union[str, List[str]]:
    """
    Convenience function to normalize property types
    
    Args:
        property_type: Property type(s) to normalize
        
    Returns:
        Normalized property type(s)
    """
    return property_type_mapper.normalize_to_api_format(property_type)


def get_section8_property_types() -> List[str]:
    """
    Convenience function to get Section 8 suitable property types
    
    Returns:
        List of property type codes suitable for Section 8
    """
    return property_type_mapper.get_section8_types()


def build_property_search_params(property_types: Optional[Union[str, List[str]]] = None) -> Dict[str, str]:
    """
    Convenience function to build property search parameters
    
    Args:
        property_types: Property types to search for
        
    Returns:
        Search parameters for Real Estate API
    """
    return property_type_mapper.build_search_parameters(property_types, section8_only=True)
