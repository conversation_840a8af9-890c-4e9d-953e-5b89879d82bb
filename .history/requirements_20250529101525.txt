# Section 8 Investor Pro - Python Dependencies

# Core framework
asyncio-mqtt==0.11.0
asyncpg==0.29.0
aiohttp==3.9.1
aiofiles==23.2.1

# Data processing
pandas==2.1.4
numpy==1.25.2

# Database
psycopg2-binary==2.9.9
sqlalchemy==2.0.23

# Environment and configuration
python-dotenv==1.0.0
pydantic==2.5.2
pydantic-settings==2.1.0

# HTTP requests and APIs
requests==2.31.0
httpx==0.25.2

# Email and notifications
resend==0.6.0

# Scheduling and timing
APScheduler==3.10.4
schedule==1.2.0

# Data validation and parsing
marshmallow==3.20.2
jsonschema==4.20.0

# Logging and monitoring
structlog==23.2.0
prometheus-client==0.19.0

# Geographic and mapping
geopy==2.4.1
shapely==2.0.2

# Mathematical calculations
scipy==1.11.4

# Web framework (for dashboard/API)
fastapi==0.104.1
uvicorn==0.24.0
jinja2==3.1.2
python-multipart==0.0.6

# Security
cryptography==41.0.8
bcrypt==4.1.2

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-mock==3.12.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Deployment
gunicorn==21.2.0
docker==6.1.3

# Performance monitoring
memory-profiler==0.61.0
psutil==5.9.6

# Data export
openpyxl==3.1.2
xlsxwriter==3.1.9

# Utilities
click==8.1.7
rich==13.7.0
tqdm==4.66.1
python-dateutil==2.8.2
pytz==2023.3

# Optional: Machine learning for advanced analysis
scikit-learn==1.3.2

# Optional: Advanced plotting
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
