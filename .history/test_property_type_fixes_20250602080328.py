#!/usr/bin/env python3
"""
Test script to verify property type mapping fixes
"""

import sys
import os
import json
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_config_property_types():
    """Test that config.json has correct property types"""
    
    print("🔧 Testing Config Property Types")
    print("=" * 50)
    
    try:
        with open("config.json", "r") as f:
            config = json.load(f)
        
        # Check property specifications
        property_specs = config.get("investment_criteria", {}).get("property_specifications", {})
        property_types = property_specs.get("property_types", [])
        property_mapping = property_specs.get("property_type_mapping", {})
        section8_types = property_specs.get("section8_preferred_types", [])
        
        print(f"✅ Property types: {property_types}")
        print(f"✅ Property mapping: {property_mapping}")
        print(f"✅ Section 8 preferred: {section8_types}")
        
        # Verify correct API format
        expected_types = ["SFR", "MFR"]
        if property_types == expected_types:
            print("✅ Property types are in correct API format")
        else:
            print(f"❌ Property types should be {expected_types}, got {property_types}")
            return False
        
        # Verify mapping exists
        if property_mapping and "SFR" in property_mapping and "MFR" in property_mapping:
            print("✅ Property type mapping is present")
        else:
            print("❌ Property type mapping is missing or incomplete")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading config: {e}")
        return False

def test_api_route_property_types():
    """Test API route property type handling"""
    
    print("\n🔧 Testing API Route Property Types")
    print("=" * 50)
    
    try:
        # Check the API route file
        api_route_path = Path("api/property-search/route.ts")
        if api_route_path.exists():
            with open(api_route_path, "r") as f:
                content = f.read()
            
            # Check for correct property type usage (both quoted and unquoted)
            if 'property_type: "SFR"' in content or "property_type: 'SFR'" in content:
                print("✅ API route uses SFR property type")
            else:
                print("❌ API route missing SFR property type")
                return False
            
            # Check for no comma-separated property types
            if '"SFR,MFR"' in content or '"SFR, MFR"' in content:
                print("❌ API route still has comma-separated property types")
                return False
            else:
                print("✅ API route does not use comma-separated property types")
            
            return True
        else:
            print("⚠️  API route file not found, skipping test")
            return True
            
    except Exception as e:
        print(f"❌ Error checking API route: {e}")
        return False

def test_production_monitor_fixes():
    """Test production monitor property type fixes"""
    
    print("\n🔧 Testing Production Monitor Fixes")
    print("=" * 50)
    
    try:
        with open("production_monitor.py", "r") as f:
            content = f.read()
        
        # Check for separate property type searches
        if 'property_types = ["SFR", "MFR"]' in content:
            print("✅ Production monitor searches both SFR and MFR separately")
        else:
            print("❌ Production monitor not configured for separate property type searches")
            return False
        
        # Check for no comma-separated property types
        if '"SFR,MFR"' in content:
            print("❌ Production monitor still has comma-separated property types")
            return False
        else:
            print("✅ Production monitor does not use comma-separated property types")
        
        # Check for all_properties aggregation
        if 'all_properties.extend(properties)' in content:
            print("✅ Production monitor aggregates properties from multiple searches")
        else:
            print("❌ Production monitor missing property aggregation")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking production monitor: {e}")
        return False

def test_corrected_api_file():
    """Test corrected API test file"""
    
    print("\n🔧 Testing Corrected API Test File")
    print("=" * 50)
    
    try:
        with open("test_corrected_api.py", "r") as f:
            content = f.read()
        
        # Check for separate SFR and MFR tests
        if '"property_type": "SFR"' in content and '"property_type": "MFR"' in content:
            print("✅ Test file has separate SFR and MFR tests")
        else:
            print("❌ Test file missing separate property type tests")
            return False
        
        # Check for no comma-separated property types
        if '"SFR,MFR"' in content:
            print("❌ Test file still has comma-separated property types")
            return False
        else:
            print("✅ Test file does not use comma-separated property types")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking test file: {e}")
        return False

def test_property_type_mapper():
    """Test property type mapper functionality"""
    
    print("\n🔧 Testing Property Type Mapper")
    print("=" * 50)
    
    try:
        from src.utils.property_type_mapper import property_type_mapper
        
        # Test basic functionality
        sfr_result = property_type_mapper.normalize_to_api_format("SINGLE_FAMILY")
        mfr_result = property_type_mapper.normalize_to_api_format("MULTI_FAMILY")
        
        if sfr_result == "SFR" and mfr_result == "MFR":
            print("✅ Property type mapper normalizes correctly")
        else:
            print(f"❌ Property type mapper failed: SFR={sfr_result}, MFR={mfr_result}")
            return False
        
        # Test search parameters
        params = property_type_mapper.build_search_parameters(["SFR", "MFR"])
        if "property_type" in params and params["property_type"] in ["SFR", "MFR"]:
            print("✅ Property type mapper generates correct search parameters")
        else:
            print(f"❌ Property type mapper failed to generate correct params: {params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing property type mapper: {e}")
        return False

def main():
    """Run all tests"""
    
    print("🚀 Testing Property Type Mapping Fixes")
    print("=" * 60)
    
    tests = [
        test_config_property_types,
        test_api_route_property_types,
        test_production_monitor_fixes,
        test_corrected_api_file,
        test_property_type_mapper
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All property type mapping fixes are working correctly!")
        print("\n✅ Key Fixes Implemented:")
        print("   • Config uses SFR/MFR instead of SINGLE_FAMILY/MULTI_FAMILY")
        print("   • Property type mapper handles conversions")
        print("   • API calls use separate requests for each property type")
        print("   • No comma-separated property types in API calls")
        print("   • Production monitor aggregates results from multiple searches")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
